2025/04/18-04:15:17.606 23fc Reusing MANIFEST C:\Users\<USER>\OneDrive\Desktop\scrape\usfood\userdata\Default\IndexedDB\https_order.usfoods.com_0.indexeddb.leveldb/MANIFEST-000001
2025/04/18-04:15:17.607 23fc Recovering log #3
2025/04/18-04:15:17.609 23fc Reusing old log C:\Users\<USER>\OneDrive\Desktop\scrape\usfood\userdata\Default\IndexedDB\https_order.usfoods.com_0.indexeddb.leveldb/000003.log 
2025/04/18-04:15:17.631 5630 Level-0 table #5: started
2025/04/18-04:15:17.658 5630 Level-0 table #5: 18224 bytes OK
2025/04/18-04:15:17.664 5630 Delete type=0 #3
2025/04/18-04:15:17.669 1644 Manual compaction at level-0 from '\x00\x02\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x03\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.678 1644 Level-0 table #7: started
2025/04/18-04:15:17.705 1644 Level-0 table #7: 3343 bytes OK
2025/04/18-04:15:17.708 1644 Delete type=0 #4
2025/04/18-04:15:17.708 1644 Manual compaction at level-0 from '\x00\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.709 1644 Manual compaction at level-1 from '\x00\x01\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at '\x00\x01\x00\x00\xc8\x0a\x00s\x00n\x00e\x00a\x00k\x00-\x00p\x00e\x00e\x00k' @ 1884 : 0
2025/04/18-04:15:17.709 1644 Compacting 1@1 + 1@2 files
2025/04/18-04:15:17.721 1644 Generated table #8@1: 56 keys, 1060 bytes
2025/04/18-04:15:17.721 1644 Compacted 1@1 + 1@2 files => 1060 bytes
2025/04/18-04:15:17.723 1644 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/04/18-04:15:17.724 1644 Delete type=2 #7
2025/04/18-04:15:17.725 1644 Manual compaction at level-1 from '\x00\x01\x00\x00\xc8\x0a\x00s\x00n\x00e\x00a\x00k\x00-\x00p\x00e\x00e\x00k' @ 1884 : 0 .. '\x00\x02\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.726 5630 Level-0 table #10: started
2025/04/18-04:15:17.741 5630 Level-0 table #10: 579 bytes OK
2025/04/18-04:15:17.743 5630 Delete type=2 #5
2025/04/18-04:15:17.743 5630 Delete type=0 #6
2025/04/18-04:15:17.748 1644 Manual compaction at level-0 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.759 1644 Manual compaction at level-1 from '\x00\x03\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at '\x00\x03\x00\x00\xc8\x12\x00n\x00o\x00t\x00i\x00f\x00i\x00c\x00a\x00t\x00i\x00o\x00n\x00-\x00c\x00o\x00u\x00n\x00t' @ 1918 : 0
2025/04/18-04:15:17.760 1644 Compacting 1@1 + 1@2 files
2025/04/18-04:15:17.783 1644 Generated table #11@1: 22 keys, 628 bytes
2025/04/18-04:15:17.783 1644 Compacted 1@1 + 1@2 files => 628 bytes
2025/04/18-04:15:17.790 1644 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/04/18-04:15:17.791 1644 Delete type=2 #10
2025/04/18-04:15:17.793 1644 Manual compaction at level-1 from '\x00\x03\x00\x00\xc8\x12\x00n\x00o\x00t\x00i\x00f\x00i\x00c\x00a\x00t\x00i\x00o\x00n\x00-\x00c\x00o\x00u\x00n\x00t' @ 1918 : 0 .. '\x00\x04\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.797 1644 Level-0 table #13: started
2025/04/18-04:15:17.805 1644 Level-0 table #13: 376 bytes OK
2025/04/18-04:15:17.806 1644 Delete type=2 #8
2025/04/18-04:15:17.806 1644 Delete type=0 #9
2025/04/18-04:15:17.807 1644 Manual compaction at level-0 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/04/18-04:15:17.807 1644 Manual compaction at level-1 from '\x00\x04\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at '\x00\x04\x00\x00\xc8\x09\x00e\x00d\x00i\x00t\x00-\x00c\x00a\x00r\x00t' @ 1934 : 0
2025/04/18-04:15:17.807 1644 Compacting 1@1 + 1@2 files
2025/04/18-04:15:17.817 1644 Generated table #14@1: 6 keys, 275 bytes
2025/04/18-04:15:17.817 1644 Compacted 1@1 + 1@2 files => 275 bytes
2025/04/18-04:15:17.817 1644 compacted to: files[ 0 0 1 0 0 0 0 ]
2025/04/18-04:15:17.818 1644 Delete type=2 #13
2025/04/18-04:15:17.819 1644 Manual compaction at level-1 from '\x00\x04\x00\x00\xc8\x09\x00e\x00d\x00i\x00t\x00-\x00c\x00a\x00r\x00t' @ 1934 : 0 .. '\x00\x05\x00\x00\x00' @ 0 : 0; will stop at (end)
