{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Chromium.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-135.0.7049.42\\chrome-win64\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chromium PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\.cache\\puppeteer\\chrome\\win64-135.0.7049.42\\chrome-win64\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "DB4B7F0E643C25666379A0D92862171E18AD4F121A6B2096F807E9F09A99440F"}, "extensions": {"ui": {"developer_mode": "E65B2455D6A238CDE59D7C6CE5AE00B8BA95795A31E699F8F711AD5ABC1E4697"}}, "homepage": "37DA9F5363252561E47FDBCA74D51015210D86D98EC33C14045F48296284EC6C", "homepage_is_newtabpage": "161EFE089AB176747F95BFE0F38A54C1374588A8F9411696267A0535AC967FFB", "session": {"restore_on_startup": "2CDFEF61A5E4AE202A60EE5AEFAA1A5EA602743E14CF8D843E9A87D96A34739F", "startup_urls": "E2159A93867B5F8DB64BEBA95BC1666747C819A09233ECE776D5938684E58F10"}}, "browser": {"show_home_button": "E26496E2D44C929A94BF0D07B078FCE268E00B241F5CB01DBBA094E77F76E0DE"}, "default_search_provider_data": {"template_url_data": "758E5D06993102E2E8544C289F234C86292D1009691C955B6B2709C0C526E798"}, "enterprise_signin": {"policy_recovery_token": "02FA5623B7B25A71245E454168E028AA6B6C3AE0BD075BD1A9365CC35AF00D06"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "482A46859F1DD06A8D541D68CF19EEFECA864F053D233F87A9C0B0686B55330E", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "CCDA42CBD60E7E4ACBA772B0847B96BCCBA1D2D5AFBE2805DE50D3CEF4348909"}, "ui": {"developer_mode": "B84405234068BD2928933779CF3AC147D5D2901E5E7F1A34FE5D864588B81E06"}}, "google": {"services": {"account_id": "62389EF4C42A30924E668092A83B43E1FDB488C13C4A39923761D1D5C53E04CC", "last_signed_in_username": "8E974A73A99C0FAE2F8793AB98C8DFC9EA60836514F8EFBB8944A322639FE9C7", "last_username": "6162AFE85AE186B83D3AF6AA919FFC1E9C8965DA267051461A5B85EA77C414CF"}}, "homepage": "DE707E8B6BD2AFE88D6FDF9AABAF3990DA7BC0C285DAAFCFBA0777C91B00D541", "homepage_is_newtabpage": "1D46FE37ECC06C3CAB90D14A2B459809E600C327899602A48F3AE9E68210D5D8", "media": {"cdm": {"origin_data": "453AB1BA6A4B1CD4A8F19614DA89A0C306322E2B420F7D9CE11F2649BAFD04E6"}, "storage_id_salt": "CFB59160339A4E98EE41961025CAC2399D395DD687BBC270388618AAEAFDC953"}, "pinned_tabs": "07B006C19B770F2B65A14DAE2BDFB9AD3C4C8481477448292E1E1707F1E8A590", "prefs": {"preference_reset_time": "9BFB4E5DB1F9A3AFB9BAE7ECF34E244E9EFFB1C50106D26265560B4884C47833"}, "safebrowsing": {"incidents_sent": "954F466F582684CBEE7266DCE1AF0BF798A5D6D20E1124E75E052CBCA338BDBA"}, "search_provider_overrides": "0263E5171ADD65E50B0DE56F0ED9749DB8922323F65B2B232FF9811D2CC8F63D", "session": {"restore_on_startup": "F4212B2F138C23234D9CD04F1D95901459A9924D9E7E00157919626EC48FC0F9", "startup_urls": "28C45695998B5C728A3050CE760890222E77633FD303841BF2DA25259EF65836"}}, "super_mac": "95499C687924AE7CB18D994C6CDF70DA1DE76FAFAB573E8C070733C06FE1068E"}}