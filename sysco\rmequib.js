const fs = require('fs');

// Read the JSON file
fs.readFile('greco.json', 'utf8', (err, data) => {
  if (err) {
    console.error('Error reading file:', err);
    return;
  }

  try {
    // Parse JSON data
    let jsonData = JSON.parse(data);

    // Filter out objects with std_class="Equipment & Supplies"
    jsonData = jsonData.filter(item => item.std_class !== 'Equipment & Supplies');

    // Write the updated data back to the file
    fs.writeFile('greco.json', JSON.stringify(jsonData, null, 2), 'utf8', (err) => {
      if (err) {
        console.error('Error writing file:', err);
        return;
      }
      console.log('Successfully removed objects with std_class="Equipment & Supplies"');
    });
  } catch (err) {
    console.error('Error parsing JSON:', err);
  }
});