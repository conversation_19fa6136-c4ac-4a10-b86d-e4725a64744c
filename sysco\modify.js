const fs = require('fs').promises;

async function processSyscoData(inputFile, outputFile, mismatchFile, packsizeMismatchFile) {
  try {
    const data = await fs.readFile(inputFile, 'utf8');
    const jsonData = JSON.parse(data);
    
    let mismatchCount = 0;
    let packsizeMismatchCount = 0;
    const mismatchData = [];
    const packsizeMismatchData = [];
    
    const updatedData = jsonData.map(obj => {
      const newObj = { ...obj };
      
      // Step 1: Process packsize
      let packsize = newObj.packsize || '';
      const packsizeClean = packsize.replace(/\s*(CS|EA|BOT|AVG|AV|AVA)$/i, '').trim();
      
      // Handle patterns like "aXb" or "aXbXc" by ignoring them and extracting the leading number
      let quantity = 1;
      let unittype = '';
      const packsizeParts = packsizeClean.match(/^([\d./]+)(?:\/([\dX]+))?(\s*(\w+|#A|#AVE|#AVG|#)?)$/i);
      
      if (packsizeParts) {
        const numericPart = packsizeParts[1];
        const xPart = packsizeParts[2] || '';
        let unit = packsizeParts[4] || '';
        
        // Check if unit contains numbers (invalid)
        if (unit && /\d/.test(unit)) {
          console.log(`Invalid unit type with numbers: ${unit}`);
          packsizeMismatchData.push(newObj);
          packsizeMismatchCount++;
          newObj.quantity = '';
          newObj.unittype = '';
          return newObj;
        }
        
        // Standardize unit
        if (unit.match(/^(#|#A|#AVE|#AVG|LBA|LBS)$/i)) unit = 'LB';
        if (unit.match(/^Z$/i)) unit = 'OZ';
        unittype = unit.toUpperCase();
        
        // Handle xPart (e.g., "11X15" or "9X3")
        if (xPart && xPart.includes('X')) {
          // Ignore xPart for quantity calculation, treat as mismatch
          console.log(`Packsize with X pattern ignored: ${packsize}`);
          quantity = parseFloat(numericPart) || 1;
          if (unit) {
            // If there's a valid unit, keep it
            newObj.quantity = quantity;
            newObj.unittype = unittype;
          } else {
            // No valid unit, log as mismatch
            packsizeMismatchData.push(newObj);
            packsizeMismatchCount++;
            newObj.quantity = '';
            newObj.unittype = '';
          }
        } else {
          // Calculate quantity
          if (numericPart.includes('/')) {
            const numbers = numericPart.split('/').map(Number);
            quantity = numbers.reduce((a, b) => a * b, 1);
          } else if (numericPart.includes('-')) {
            const [start, end] = numericPart.split('-').map(Number);
            quantity = (start + end) / 2;
          } else if (numericPart.includes('#')) {
            const parts = numericPart.split('#');
            quantity = parts[0] ? parseFloat(parts[0]) : 1;
          } else {
            quantity = parseFloat(numericPart) || 1;
          }
          newObj.quantity = quantity;
          newObj.unittype = unittype;
        }
      } else {
        newObj.quantity = '';
        newObj.unittype = '';
        if (packsize && !isNaN(packsize)) {
          console.log(`Packsize only numeric: ${packsize}`);
          packsizeMismatchData.push(newObj);
          packsizeMismatchCount++;
        } else if (packsize) {
          console.log(`Packsize pattern mismatch: ${packsize}`);
          packsizeMismatchData.push(newObj);
          packsizeMismatchCount++;
        }
      }
      
      // Step 2: Extract price information
      const caseprice = newObj.caseprice ? parseFloat(newObj.caseprice.replace('$', '').split(' ')[0]) : null;
      const unitprice = newObj.unitprice ? parseFloat(newObj.unitprice.replace('$', '').split(' ')[0]) : null;
      const caseUnit = newObj.caseprice ? newObj.caseprice.split(' ').pop().toUpperCase() : '';
      const unitUnit = newObj.unitprice ? newObj.unitprice.split(' ').pop().toUpperCase() : '';
      
      // Step 3: Handle OZ to LB conversion
      quantity = newObj.quantity;
      unittype = newObj.unittype;
      
      if (unittype === 'OZ' && (caseUnit.match(/^(LB|LBA|LBS)$/i) || unitUnit.match(/^(LB|LBA|LBS)$/i))) {
        quantity = quantity * 0.0625; // Convert OZ to LB (1 OZ = 0.0625 LB)
        unittype = 'LB';
        newObj.quantity = quantity;
        newObj.unittype = unittype;
      }
      
      // Step 4: Calculate price and portionprice
      if (caseUnit.match(/^(CS|EA)$/i)) {
        newObj.price = caseprice;
        if (quantity) {
          newObj.portionprice = caseprice / quantity;
        }
      } else if (caseprice === null && unitUnit.match(/^(EA)$/i)) {
        newObj.price = unitprice;
        if (quantity) {
          newObj.portionprice = unitprice / quantity;
        }
      } else if (caseUnit === unittype || caseUnit.match(/^(LB|LBA|LBS)$/i) && unittype === 'LB') {
        newObj.portionprice = caseprice;
        if (quantity) {
          newObj.price = caseprice * quantity;
        }
      } else if (unitUnit === unittype || unitUnit.match(/^(LB|LBA|LBS)$/i) && unittype === 'LB') {
        newObj.portionprice = unitprice;
        if (quantity) {
          newObj.price = unitprice * quantity;
        }
      } else {
        mismatchCount++;
        mismatchData.push(newObj);
        return null;
      }
      
      return newObj;
    }).filter(obj => obj !== null);
    
    await fs.writeFile(outputFile, JSON.stringify(updatedData, null, 2));
    await fs.writeFile(mismatchFile, JSON.stringify(mismatchData, null, 2));
    await fs.writeFile(packsizeMismatchFile, JSON.stringify(packsizeMismatchData, null, 2));
    
    console.log(`Total objects processed: ${jsonData.length}`);
    console.log(`Price mismatch objects found: ${mismatchCount}`);
    console.log(`Packsize mismatch objects found: ${packsizeMismatchCount}`);
    console.log(`Processed data saved to: ${outputFile}`);
    console.log(`Price mismatch data saved to: ${mismatchFile}`);
    console.log(`Packsize mismatch data saved to: ${packsizeMismatchFile}`);
    
  } catch (error) {
    console.error(`Error processing ${inputFile}:`, error.message);
  }
}

async function main() {
  await processSyscoData('fullsysco.json', 'sysco_db_tmp.json', 'mismatch.json', 'packsize_mismatch.json');
}

main();