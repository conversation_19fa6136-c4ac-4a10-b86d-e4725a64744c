const fs = require("fs");
const path = require("path");

(async () => {
  // Load progress.json
  const progressFilePath = path.resolve(__dirname, "progress.json");
  const progress = JSON.parse(fs.readFileSync(progressFilePath, "utf8"));
  const scrapedSubcategories = progress.scrapedSubcategories || [];

  // Load cat.json
  const catJsonPath = path.resolve(__dirname, "cat.json");
  const categories = JSON.parse(fs.readFileSync(catJsonPath, "utf8"));

  // Function to clean subcategory name (same logic as your original script)
  const cleanSubcatName = (subcat) => {
    return subcat.replace(/[^a-zA-Z0-9]/g, "");
  };

  // Prepare arrays for empty and non-empty subcategories
  let emptySubcategories = [];
  let nonEmptyCount = 0;

  // Check each subcategory file
  for (const subcatFileName of scrapedSubcategories) {
    const subcatFilePath = path.resolve(__dirname, subcatFileName);

    // Check if the file exists and its content length
    if (fs.existsSync(subcatFilePath)) {
      const subcatData = JSON.parse(fs.readFileSync(subcatFilePath, "utf8"));

      if (subcatData.length === 0) {
        // Find the matching category in cat.json
        const cleanedFileName = subcatFileName.replace(".json", "");
        const matchingCategory = categories.find((cat) => {
          const cleanedSubcat = cleanSubcatName(cat.subcat);
          return cleanedSubcat === cleanedFileName;
        });

        if (matchingCategory) {
          emptySubcategories.push(matchingCategory);
          console.log(`Found empty subcategory: ${subcatFileName}`);
        } else {
          console.log(
            `Warning: No matching category found in cat.json for ${subcatFileName}`
          );
        }
      } else {
        nonEmptyCount++;
        console.log(
          `Subcategory ${subcatFileName} is not empty (${subcatData.length} products)`
        );
      }
    } else {
      console.log(`Warning: File ${subcatFileName} not found on disk`);
    }
  }

  // Save empty subcategories to emptyrevise.json
  const emptyReviseFilePath = path.resolve(__dirname, "emptyrevise.json");
  fs.writeFileSync(
    emptyReviseFilePath,
    JSON.stringify(emptySubcategories, null, 2)
  );
  console.log(
    `Saved ${emptySubcategories.length} empty subcategories to ${emptyReviseFilePath}`
  );

  // Log final stats
  console.log(`\nSummary:`);
  console.log(`Empty subcategories found: ${emptySubcategories.length}`);
  console.log(`Non-empty subcategories: ${nonEmptyCount}`);
})();
