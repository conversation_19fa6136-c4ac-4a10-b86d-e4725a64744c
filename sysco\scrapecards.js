const puppeteer = require('puppeteer');
const fs = require('fs');

const categories = JSON.parse(fs.readFileSync('cats.json', 'utf8'));

let progress;
if (fs.existsSync('progress.json')) {
  progress = JSON.parse(fs.readFileSync('progress.json', 'utf8'));
} else {
  progress = { currentCategoryIndex: 0, currentPage: 1 };
}

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    userDataDir: './user_data'
  });
  const page = await browser.newPage();

  let { currentCategoryIndex, currentPage } = progress;

  if (currentCategoryIndex === 0 && currentPage === 1) {
    const firstUrl = `${categories[0]}&page=1&typeAhead=false`;
    await page.goto(firstUrl);
    console.log('Please log in manually. Waiting for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }

  while (currentCategoryIndex < categories.length) {
    const categoryUrl = categories[currentCategoryIndex];
    let pageNumber = currentPage; // Initialize only once per category
  
    while (true) {
      const url = `${categoryUrl}&page=${pageNumber}&typeAhead=false`;
      console.log(`Scraping page ${pageNumber} of category ${currentCategoryIndex}`);
      await page.goto(url);
      await new Promise(resolve => setTimeout(resolve, 5000));
  
      const productWrapper = await page.$('.catalog-cards-wrapper');
      if (productWrapper) {
        const category = await page.$eval('[data-id="breadcrumb_category_level1"]', el => el.innerText).catch(() => null);
        const productCards = await page.$$('.catalog-cards-wrapper .product-card-container');
        const products = [];
  
        for (const card of productCards) {
          let product = {};
          try {
            product.productURL = await card.$eval('a.product-card-link', el => el.href).catch(() => null);
            product.productImage = await card.$eval('.product-image img', el => el.src).catch(() => null);
            const dataId = await card.evaluate(el => el.getAttribute('data-id')).catch(() => null);
            product.productNumber = dataId ? dataId.split('_').pop() : null;
            product.name = await card.$eval('.product-name', el => el.innerText).catch(() => null);
            product.brand = await card.$eval('.brand', el => el.innerText).catch(() => null);
            const priceElement = await card.$('.price-value');
            if (priceElement) {
              product.price = await priceElement.evaluate(el => el.innerText).catch(() => null);
            } else {
              const seePricingElement = await card.$('.see-pricing-info-text');
              product.price = seePricingElement ? await seePricingElement.evaluate(el => el.innerText).catch(() => null) : null;
            }
            product.category = category;
            products.push(product);
          } catch (error) {
            console.log(`Error processing product card on page ${pageNumber} of category ${currentCategoryIndex}: ${error.message}`);
            continue;
          }
        }
  
        for (const product of products) {
          fs.appendFileSync('sysco.json', JSON.stringify(product) + '\n');
        }
  
        const showingText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsPageText"]', el => el.innerText).catch(() => null);
        const totalText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsTotalText"]', el => el.innerText).catch(() => null);
        let Y, Z;
        if (showingText && totalText) {
          const showingParts = showingText.split('-').map(s => s.trim());
          Y = parseInt(showingParts[1]);
          const totalParts = totalText.split(' ');
          Z = parseInt(totalParts[1]);
        } else {
          console.log(`Error: could not retrieve pagination info on page ${pageNumber} of category ${currentCategoryIndex}`);
          pageNumber += 1; // Increment to avoid infinite loop
          currentPage = pageNumber;
          fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage }));
          continue;
        }
  
        if (Y >= Z) {
          console.log(`Finished category ${currentCategoryIndex}`);
          currentCategoryIndex += 1;
          currentPage = 1;
          fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage }));
          break;
        }
  
        pageNumber += 1;
        currentPage = pageNumber;
        fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage }));
      } else {
        const noResults = await page.$('div[data-id="no_results_message_header"]');
        if (noResults) {
          console.log(`No more products in category ${currentCategoryIndex}`);
          currentCategoryIndex += 1;
          currentPage = 1;
          fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage }));
          break;
        } else {
          console.log(`Error: neither product wrapper nor no results message found on page ${pageNumber} of category ${currentCategoryIndex}`);
          pageNumber += 1; // Increment to avoid infinite loop
          currentPage = pageNumber;
          fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage }));
          continue;
        }
      }
    }
  }
  // await browser.close();
  console.log('Scraping completed.');
})();