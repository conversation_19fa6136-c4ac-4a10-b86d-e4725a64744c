const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    userDataDir: "./user_data",
  });

  const page = await browser.newPage();
  await page.setViewport({ width: 1920, height: 1080 });
  page.setDefaultNavigationTimeout(60000);

  console.log("Navigating to initial page...");
  await page.goto("https://order.usfoods.com/desktop/search/browse", {
    waitUntil: "networkidle2",
  });

  console.log("Clicking zip code change button...");
  await page.waitForSelector(".zip-code-change-button");
  await page.click(".zip-code-change-button");

  console.log("Entering zip code...");
  await page.waitForSelector("#ion-input-1");
  await page.type("#ion-input-1", "85003");

  console.log("Saving zip code...");
  await page.waitForSelector('ion-button[data-cy="save-zip-code-button"]');
  await page.click('ion-button[data-cy="save-zip-code-button"]');

  console.log("Navigating to categories page...");
  await page.goto(
    "https://order.usfoods.com/desktop/search/browse/categories",
    {
      waitUntil: "networkidle2",
    }
  );

  // Load existing data for progress tracking
  let result = [];
  const filePath = path.resolve(__dirname, "cat.json"); // Absolute path to cat.json
  if (fs.existsSync(filePath)) {
    console.log(`Loading existing data from ${filePath}`);
    result = JSON.parse(fs.readFileSync(filePath));
  } else {
    console.log(`No existing cat.json found at ${filePath}. Starting fresh.`);
  }
  const processedMainCats = new Set(result.map((item) => item.maincat));

  // Scrape main categories using image IDs
  console.log("Scraping main categories...");
  const mainCategories = await page.evaluate(() => {
    const cards = document.querySelectorAll(".browse-category-items ion-card");
    return Array.from(cards)
      .map((card) => {
        const name = card
          .querySelector(".browse-category-card-description div")
          ?.innerText.trim();
        const imgSrc = card.querySelector("img")?.src;
        if (!name || !imgSrc) {
          console.log("Missing name or image for a main category");
          return null;
        }
        const id = imgSrc.split("/").pop().split(".")[0]; // e.g., "20"
        const url = `https://order.usfoods.com/desktop/search/browse/categories/${id}`;
        return { name, url };
      })
      .filter((item) => item);
  });

  console.log("Main categories scraped:");
  mainCategories.forEach((cat, index) => {
    console.log(`${index + 1}. ${cat.name} - ${cat.url}`);
  });

  // Loop over main category URLs
  for (const { name: mainCatName, url: mainCatUrl } of mainCategories) {
    if (processedMainCats.has(mainCatName)) {
      console.log(`Skipping already processed: ${mainCatName}`);
      continue;
    }

    try {
      console.log(`Navigating to ${mainCatName} at ${mainCatUrl}...`);
      await page.goto(mainCatUrl, { waitUntil: "networkidle2" });

      console.log(`Waiting for subcategories for ${mainCatName}...`);
      await page.waitForSelector(".browse-sub-category-items", {
        timeout: 60000,
      });

      // Scrape subcategories
      const subCategories = await page.evaluate((mainCat) => {
        const subContainer = document.querySelector(
          ".browse-sub-category-items"
        );
        if (!subContainer) {
          console.log(`No .browse-sub-category-items found for ${mainCat}`);
          return [];
        }
        const subCards = subContainer.querySelectorAll("ion-card");
        console.log(
          `Found ${subCards.length} subcategory cards for ${mainCat}`
        );
        return Array.from(subCards)
          .map((card) => {
            const subCatName = card
              .querySelector(".browse-sub-category-card-description div")
              ?.innerText.trim();
            const imgSrc = card.querySelector("img")?.src;
            if (!subCatName || !imgSrc) {
              console.log(
                `Missing name or image for a subcategory in ${mainCat}`
              );
              return null;
            }
            const id = imgSrc.split("/").pop().split(".")[0]; // e.g., "335"
            const url = `https://order.usfoods.com/desktop/search?searchFilterProperties=${id}&categoryId=${id}&pageType=browse&originSearchPage=catalog`;
            return { subcat: subCatName, maincat: mainCat, url };
          })
          .filter((item) => item);
      }, mainCatName);

      console.log(
        `Scraped ${subCategories.length} subcategories for ${mainCatName}:`
      );
      subCategories.forEach((sub, index) => {
        console.log(`  ${index + 1}. ${sub.subcat} - ${sub.url}`);
      });

      // Append and save to cat.json
      result.push(...subCategories);
      try {
        fs.writeFileSync(filePath, JSON.stringify(result, null, 2));
        console.log(
          `Saved ${subCategories.length} subcategories to ${filePath}`
        );
      } catch (writeError) {
        console.error(`Failed to write to ${filePath}:`, writeError);
      }
    } catch (error) {
      console.error(`Error with ${mainCatName}:`, error);
    }
  }

  console.log("Categories scraped. Final result saved to:", filePath);
})();
