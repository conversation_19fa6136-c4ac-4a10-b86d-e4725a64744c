const fs = require("fs");
const path = require("path");

(async () => {
  // Define the directory containing the JSON files
  const productsDir = path.resolve(__dirname, "products");

  // Function to count products and summarize
  const summarizeProducts = () => {
    // Get all files in the products directory
    const files = fs
      .readdirSync(productsDir)
      .filter((file) => file.endsWith(".json"));

    let totalProducts = 0;
    const categorySummary = {};

    // Loop through each JSON file
    for (const file of files) {
      const filePath = path.join(productsDir, file);
      try {
        const data = JSON.parse(fs.readFileSync(filePath, "utf8"));

        // Ensure data is an array and count products
        if (Array.isArray(data)) {
          const productCount = data.length;
          totalProducts += productCount;

          // Use the filename (without .json) as the subcategory name
          const subcatName = file.replace(".json", "");
          categorySummary[subcatName] = productCount;

          console.log(`${subcatName}: ${productCount} products`);
        } else {
          console.log(`Warning: ${file} is not a valid product array`);
        }
      } catch (error) {
        console.error(`Error reading or parsing ${file}: ${error.message}`);
      }
    }

    // Log the summary
    console.log("\nCategory Summary:");
    for (const [subcat, count] of Object.entries(categorySummary)) {
      console.log(`${subcat}: ${count} products`);
    }
    console.log(
      `\nTotal number of products across all categories: ${totalProducts}`
    );
  };

  // Execute the summarization
  summarizeProducts();
})();
