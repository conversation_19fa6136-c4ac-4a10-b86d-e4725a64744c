const fs = require("fs");
const path = require("path");

(async () => {
  // Define directories
  const productsDir = path.resolve(__dirname, "products");
  const outputFilePath = path.resolve(__dirname, "all.json");

  // Function to merge JSON files without duplicates
  const mergeProducts = () => {
    // Get all JSON files in the products directory
    const files = fs
      .readdirSync(productsDir)
      .filter((file) => file.endsWith(".json"));

    // Object to store unique products by productNumber
    const uniqueProducts = {};

    // Loop through each JSON file
    for (const file of files) {
      const filePath = path.join(productsDir, file);
      try {
        const data = JSON.parse(fs.readFileSync(filePath, "utf8"));

        // Ensure data is an array
        if (Array.isArray(data)) {
          console.log(`Processing ${file} (${data.length} products)`);

          // Add products to uniqueProducts, using productNumber as key
          for (const product of data) {
            if (product.productNumber) {
              // Only add if productNumber doesn't exist yet
              if (!uniqueProducts[product.productNumber]) {
                uniqueProducts[product.productNumber] = product;
              } else {
                console.log(
                  `Duplicate found and skipped: ${product.productNumber} in ${file}`
                );
              }
            } else {
              console.log(
                `Warning: Product in ${file} missing productNumber, skipping`
              );
            }
          }
        } else {
          console.log(`Warning: ${file} is not a valid product array`);
        }
      } catch (error) {
        console.error(`Error reading or parsing ${file}: ${error.message}`);
      }
    }

    // Convert uniqueProducts object to an array
    const mergedProducts = Object.values(uniqueProducts);

    // Write the merged data to all.json in the main directory
    fs.writeFileSync(outputFilePath, JSON.stringify(mergedProducts, null, 2));
    console.log(
      `\nMerged ${mergedProducts.length} unique products into ${outputFilePath}`
    );
  };

  // Execute the merge
  mergeProducts();
})();
