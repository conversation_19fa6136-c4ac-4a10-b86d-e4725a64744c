const fs = require('fs').promises;

async function cleanMismatches() {
  try {
    // Read the JSON file
    const data = await fs.readFile('mismatches.json', 'utf8');
    let jsonData = JSON.parse(data);

    // Ensure jsonData is an array
    if (!Array.isArray(jsonData)) {
      throw new Error('mismatches.json must contain an array of objects');
    }

    // Track initial count
    const initialCount = jsonData.length;

    // Filter objects where either unitprice or caseprice starts with $
    const validData = jsonData.filter(item => {
      // Check if unitprice and caseprice exist, are not empty, and start with $
      const unitPriceValid = item.unitprice && item.unitprice.trim() !== '' && String(item.unitprice).trim().startsWith('$');
      const casePriceValid = item.caseprice && item.caseprice.trim() !== '' && String(item.caseprice).trim().startsWith('$');

      return unitPriceValid || casePriceValid;
    });

    // Calculate counts
    const removedCount = initialCount - validData.length;
    const remainingCount = validData.length;

    // Write the filtered data back to the file
    await fs.writeFile('mismatches.json', JSON.stringify(validData, null, 2));

    // Log results
    console.log(`Objects removed: ${removedCount}`);
    console.log(`Objects remaining: ${remainingCount}`);
  } catch (error) {
    console.error('Error processing mismatches.json:', error.message);
  }
}

cleanMismatches();