const puppeteer = require('puppeteer');
const fs = require('fs');

const categories = JSON.parse(fs.readFileSync('missedcat.json', 'utf8'));

let progress;
if (fs.existsSync('progress.json')) {
  progress = JSON.parse(fs.readFileSync('progress.json', 'utf8'));
} else {
  progress = { currentCategoryIndex: 0, currentPage: 1, failed: [] };
}

// Initialize sysco.json as an empty array (ONLY if it doesn't exist)
if (!fs.existsSync('sysco.json')) {
  try {
      fs.writeFileSync('sysco.json', '[]');
  } catch (writeError) {
      console.error(`Failed to initialize sysco.json: ${writeError.message}`);
  }
}

(async () => {
  const browser = await puppeteer.launch({
    headless: false,
    userDataDir: './user_data',
    defaultViewport: null,
    args: ['--start-maximized']
  });
  const page = await browser.newPage();

  let { currentCategoryIndex, currentPage, failed } = progress;


  while (currentCategoryIndex < categories.length) {

      const categoryUrl = categories[currentCategoryIndex];
      let pageNumber = currentPage;
  console.log(`ondex: ${currentCategoryIndex} and url: ${categoryUrl}`)
      const url = `${categoryUrl}&page=${pageNumber}&typeAhead=false`;
      console.log(`Scraping page ${pageNumber} of category ${currentCategoryIndex}`);
      
      let retries = 3;
      let success = false;
      while (retries > 0 && !success) {
        try {
          await page.goto(url, { waitUntil: 'domcontentloaded' });
          await new Promise(resolve => setTimeout(resolve, 12000));

          let productWrapper = await page.$('div.data-grid-body');
          if (!productWrapper) {
            console.log(`Product wrapper not found. Attempting to toggle to list view on page ${pageNumber} of category ${currentCategoryIndex}`);
            const listViewButton = await page.$('[data-id="catalog_list_view_button"]');
            if (listViewButton) {
              const isClickable = await page.evaluate(el => {
                const rect = el.getBoundingClientRect();
                return rect.width > 0 && rect.height > 0 && window.getComputedStyle(el).visibility !== 'hidden';
              }, listViewButton);
              if (isClickable) {
                await listViewButton.click();
                console.log('Clicked list view button. Waiting for layout change...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                productWrapper = await page.$('div.data-grid-body');
              } else {
                console.log('List view button found but not clickable.');
              }
            } else {
              console.log('List view button not found.');
            }
          }

          if (productWrapper) {
            success = true;
            const category = await page.$eval('[data-id="breadcrumb_category_level1"]', el => el.innerText).catch(() => null);
            const productCards = await page.$$('div.data-grid-body div.row.data-grid-row.v2-product-list-item');
            const products = [];

            for (const card of productCards) {
              let product = {};
              try {
                product.productURL = await card.$eval('div.productName-wrapper a', el => el.href).catch(() => null);
                product.name = await card.$eval('div.productName-wrapper', el => el.innerText).catch(() => null);
                product.productImage = await card.$eval('img[data-id="img_product_image"]', el => el.src).catch(() => null);
                product.brand = await card.$eval('label[data-id="lbl_product_brand"]', el => el.innerText).catch(() => null);
                product.productNumber = await card.$eval('label[data-id="lbl_product_id"]', el => el.innerText).catch(() => null);
                product.packSize = await card.$eval('label[data-id="lbl_product_pack"]', el => el.innerText).catch(() => null);

                product.casePrice = null;
                product.unitPrice = null;
                const priceWrappers = await card.$$('div.price-col div.price-wrapper');
                if (priceWrappers.length === 2) {
                  const casePrice = await priceWrappers[0].$eval('div.net-price label[data-id="display-price"]', el => el.innerText).catch(() => null);
                  const unitPrice = await priceWrappers[1].$eval('div.net-price label[data-id="display-price"]', el => el.innerText).catch(() => null);
                  if (casePrice) product.casePrice = casePrice;
                  if (unitPrice) product.unitPrice = unitPrice;
                } else if (priceWrappers.length === 1) {
                  const casePrice = await priceWrappers[0].$eval('div.net-price label[data-id="display-price"]', el => el.innerText).catch(() => null);
                  if (casePrice) product.casePrice = casePrice;
                } else {
                  let priceEl = await card.$eval('div.price-col .net-price-label-container label[data-id="net-price-CS"]', el => el.innerText).catch(() => null);
                  let unitEl = await card.$eval('div.price-col .net-price-label-container label[data-id="net-price-type-CS"]', el => el.innerText).catch(() => null);
                  let casePrice = priceEl && unitEl ? `${priceEl} ${unitEl}` : null;
                  if (casePrice) {
                    product.casePrice = casePrice;
                  } else {
                    priceEl = await card.$eval('div.price-col .net-price-label-container label[data-id="net-price-EA"]', el => el.innerText).catch(() => null);
                    unitEl = await card.$eval('div.price-col .net-price-label-container label[data-id="net-price-type-EA"]', el => el.innerText).catch(() => null);
                    let unitPrice = priceEl && unitEl ? `${priceEl} ${unitEl}` : null;
                    if (unitPrice) product.unitPrice = unitPrice;
                  }
                }

                product.category = category;
                products.push(product);
              } catch (error) {
                console.log(`Error processing product card on page ${pageNumber} of category ${currentCategoryIndex}: ${error.message}`);
                continue;
              }
            }

            // Read current sysco.json, append new products, and rewrite
            let syscoData = [];
            try {
              syscoData = JSON.parse(fs.readFileSync('sysco.json', 'utf8'));
            } catch (error) {
              console.error(`Error reading sysco.json: ${error.message}`);
            }
            syscoData.push(...products);
            try {
              fs.writeFileSync('sysco.json', JSON.stringify(syscoData, null, 2));
              console.log(`Page ${pageNumber} of category ${currentCategoryIndex}: Successfully saved ${products.length} products to sysco.json`);
            } catch (writeError) {
              console.error(`Failed to write to sysco.json: ${writeError.message}`);
            }

            const showingText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsPageText"]', el => el.innerText).catch(() => null);
            const totalText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsTotalText"]', el => el.innerText).catch(() => null);
            let Y, Z;
            if (showingText && totalText) {
              const showingParts = showingText.split('-').map(s => s.trim());
              Y = parseInt(showingParts[1]);
              const totalParts = totalText.split(' ');
              Z = parseInt(totalParts[1]);
            } else {
              console.log(`Error: could not retrieve pagination info on page ${pageNumber} of category ${currentCategoryIndex}`);
              pageNumber += 1;
              currentPage = pageNumber;
              fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
              continue;
            }
            if (Y >= Z) {
              console.log(`Finished category ${currentCategoryIndex} ${Y} ${Z}`);
              currentCategoryIndex += 1;
              currentPage = 1; // Reset currentPage here
              pageNumber = 1;
              await new Promise(resolve => setTimeout(resolve, 3000)); // Add delay before next category
              fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
              break;
            }

            pageNumber += 1;
            currentPage = pageNumber;
            fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
          } else {
            const noResults = await page.$('div[data-id="no_results_message_header"]');
            if (noResults) {
              console.log(`No more products in category ${currentCategoryIndex}`);
              currentCategoryIndex += 1;
              currentPage = 1; // Reset currentPage here
              pageNumber = 1;
              // await new Promise(resolve => setTimeout(resolve, 3000)); // Add delay before next category
              fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
              break;
            } else {
              retries -= 1;
              console.log(`Retry ${3 - retries} of 3: Neither product wrapper nor no results message found on page ${pageNumber} of category ${currentCategoryIndex}`);
              if (retries === 0) {
                console.log(`Error: Failed after 3 retries. Dumping page content for debugging:`);
                const pageContent = await page.content().catch(() => 'Could not retrieve page content');
                console.log(pageContent.substring(0, 1000));
                failed = failed || [];
                failed.push(url);
                pageNumber += 1;
                currentPage = pageNumber;
                fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
                break;
              }
              await new Promise(resolve => setTimeout(resolve, 15000 + (3 - retries) * 2000));
            }
          }
        } catch (error) {
          console.log(`Error loading page ${pageNumber} of category ${currentCategoryIndex}: ${error.message}`);
          retries -= 1;
          if (retries === 0) {
            console.log(`Error: Failed after 3 retries. Moving to next page.`);
            failed = failed || [];
            failed.push(url);
            pageNumber += 1;
            currentPage = pageNumber;
            fs.writeFileSync('progress.json', JSON.stringify({ currentCategoryIndex, currentPage, failed }));
            break;
          }
          await new Promise(resolve => setTimeout(resolve, 15000 + (3 - retries) * 2000));
        }
      }
      if (!success) {
        pageNumber = currentPage;
        break;
      }
    
  }

  await browser.close();
  console.log('Scraping completed.');
})();