{"net": {"http_server_properties": {"servers": [{"anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.aws.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040485870621", "port": 443, "protocol_str": "quic"}], "anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.gcp.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 28844}, "server": "https://cdnjs.cloudflare.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 37391}, "server": "https://cdn.lrkt-in.com"}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://se.monetate.net", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://w.usabilla.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://js-agent.newrelic.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://fast.appcues.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://sdk.split.io", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://www.usfoods.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040484997464", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://static.cloud.coveo.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://dpm.demdex.net", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", true, 0], "server": "https://usfoods.demdex.net", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://usfoodsproduction10upnbvk4.org.coveo.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://d2icnbk86osaxj.cloudfront.net", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://usfoodsproduction10upnbvk4.analytics.org.coveo.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://assets.adobedtm.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://panamax-api.ama.usfoods.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://assets.usfoods.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://ui.powerreviews.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://display.powerreviews.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://order.usfoods.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://metrics.usfoods.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "server": "https://r.lrkt-in.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040525413316", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 30239}, "server": "https://android.clients.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040535303936", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 33688}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 39951}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040534362357", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 127159}, "server": "https://googleads.g.doubleclick.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040534310100", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", true, 0], "network_stats": {"srtt": 55277}, "server": "https://td.doubleclick.net", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 60985}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040534480698", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", false, 0], "network_stats": {"srtt": 64779}, "server": "https://www.google.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13392040485670921", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABMAAABodHRwczovL3VzZm9vZHMuY29tAA==", true, 0], "network_stats": {"srtt": 75555}, "server": "https://www.googletagmanager.com", "supports_spdy": true}], "supports_quic": {"address": "2600:8800:3720:be00:7d24:2494:b1d7:aed0", "used_quic": true}, "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "4G"}}}