function logJsonArrayCount(jsonData) {
  // Check if the input is an array
  if (!Array.isArray(jsonData)) {
    console.log("Error: Input is not an array");
    return;
  }

  // Log the count of objects in the array
  console.log(`Number of objects in the JSON array: ${jsonData.length}`);
}

const { log } = require("console");
// If you have your JSON in a file, you can read it first with Node.js:
const fs = require("fs");

function logJsonFileCount(filePath) {
  fs.readFile(filePath, "utf8", (err, data) => {
    if (err) {
      console.log("Error reading file:", err);
      return;
    }

    try {
      const jsonData = JSON.parse(data);
      logJsonArrayCount(jsonData);
    } catch (error) {
      console.log("Error parsing JSON:", error);
    }
  });
}

// Usage with a file
//logJsonFileCount("BagsBoxesTraysLabelsRegisterTapeDispble.json");
logJsonFileCount("0cat.json");
