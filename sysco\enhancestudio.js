// Import necessary modules
const { GoogleGenerativeAI } = require("@google/generative-ai");
const fs = require("fs").promises; // Use promises version of fs
const path = require("path");
require("dotenv").config(); // Load environment variables from .env file

// --- Configuration ---
// ***** START: Added PROGRESS_FILE constant *****
const PROGRESS_FILE = "processing_progress.json"; // File to store the next starting index
// ***** END: Added PROGRESS_FILE constant *****
const API_KEY = process.env.GEMINI_API_KEY;
const INPUT_JSON_FILE = "sham.json"; // Original input file name (used only if output doesn't exist)
const OUTPUT_JSON_FILE = "chef_enhanced.json"; // Output file where enhanced data is saved (and loaded from on restart)
const BATCH_SIZE = 25; // Process 10 products at a time
const DELAY_BETWEEN_BATCHES_MS = 20000; // User's previous delay value

// --- Gemini AI Setup ---
if (!API_KEY) {
  console.error(
    "Error: GEMINI_API_KEY not found. Please create a .env file with your API key."
  );
  process.exit(1); // Exit if API key is missing
}

const genAI = new GoogleGenerativeAI(API_KEY);

// ***** START: Keeping user's exact model, systemInstruction, and generationConfig *****
const model = genAI.getGenerativeModel({
  model: "gemini-2.0-flash", // User provided model
  systemInstruction:
 `i am going to give you names of food products that i need you to enhance and return it in a structured output ( an array) same as you took it as input( an array):
** Carefully examine the product name to understand its key features, including:
*   Product Type (e.g., "chicken breast", "beef","garlic", "mop", "Spices", "juice")
*   Product Form (e.g., "boneless", "sliced", "whole", "diced")
*   Color (e.g., "red", "white", "green")
*   Packaging (e.g., "can", "bottle", "bag")
*   Quantity and Units (e.g., "15 dozen", "5 lb", "28 oz")
* brand names
* nutritional information
** Next, enhance the name of each product by:
*   Expand any abbreviations to fully understand the product (e.g., "DFRCP" means "Disposable Food Container", "WRP" means "Wood Rolling Pin", "ct" means "count", "oz" means "ounce", "lb" means "pound", "ltr" means "liter"). 
* remove quantity and unity numbers and units
* remove unimportant information like fat grade or nutritional information
* remove brand name if you recognize brands
* if the name has lower than 4 words then add very correlated words ( for example the core product type like beef chicken ..etc) to enhance the name to be at least 4 words, the goal from this is when i search for the name in sellers websites i do not get a lot of results because the name is vague or not well defined. however the words you add should be very correlated to represent the same product.`,
    });

const generationConfig = {
  temperature: 1, // User provided config
  topP: 0.95,
  topK: 40,
  maxOutputTokens: 8192,
  responseModalities: [], // User provided config
  responseMimeType: "application/json",
};
// ***** END: Keeping user's exact model, systemInstruction, and generationConfig *****

// --- Helper Function to Add Delay ---
function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// --- Function to Generate Enhanced Names via Gemini (User's Original Version) ---
async function generateEnhancedNames(productNamesBatch) {
  console.log(
    `  Sending batch of ${productNamesBatch.length} names to Gemini...`
  );
  try {
    // Start a new chat session for each batch for simplicity
    // The system instruction and history prime the model correctly each time
    const chatSession = model.startChat({
      generationConfig,
      history: [
        // Using user's provided history exactly
        {
          role: "user",
          parts: [
            {
              text: '[  "Sunset - 6\\" Bamboo Skewers - 100 Ct",\n  "Frozen Thrifty Foods - Vanilla Ice Cream - 3 Gal Tub",\n  "Athena - Fire Roasted Grilled Eggplant Pulp - 2 kg",\n  "Chef\'s Quality - 12\\" Whole Wheat Soft Tortillas or Wraps - 12ct Pack",\n  "Sunset - TF-81-RD - 8\\" Hinged TFPP Container - 150 ct"]',
            },
          ],
        },
        {
          role: "model",
          parts: [
            {
              text: 'The user wants to process a list of food product names and enhance them based on several criteria.\n\nHere\'s the plan:\n1.  Iterate through each product name in the input array.\n2.  For each name, identify and remove metadata (sizes, weights, counts, codes like "TF-81-RD"). Metadata often follows patterns like `number unit`, `number ct`, `number kg`, `number Gal`, `number\\"`, `#number`.\n3.  Identify and remove brand names (like "Sunset", "Thrifty Foods", "Athena", "Chef\'s Quality"). Sometimes the brand name is helpful, sometimes not. The instruction says "you can remove brand names if you are sure it is a brand and does not describe the product." I will remove them where they seem like clear brands and don\'t add core product description.\n4.  Replace abbreviations (like "ct" -> "count", "Gal" -> "Gallon").\n5.  Identify the core product type. If missing, add it (e.g., "purified" -> "purified water").\n  Return the processed names as a JSON array.\n\nLet\'s process the given list:\n\n1.  **"Sunset - 6\\" Bamboo Skewers - 100 Ct"**\n    *   Remove metadata: `6\\"`, `100 Ct`\n    *   Remove brand: `Sunset`\n    *   Result: `Bamboo Skewers`\n    *   Less than 3 words. Add descriptive words. "Bamboo Skewers" is quite descriptive already. Maybe "Wooden Bamboo Skewers"? Or just keep it as "Bamboo Skewers" since it\'s specific. Let\'s stick to the core product: "Wooden Bamboo Skewers".\n    *   Final: `"Wooden Bamboo Skewers"`\n\n2.  **"Frozen Thrifty Foods - Vanilla Ice Cream - 3 Gal Tub"**\n    *   Remove metadata: `3 Gal Tub`\n    *   Remove brand: `Thrifty Foods`\n    *   Result: `Frozen Vanilla Ice Cream`\n    *   Already 3 words. Core product type "Ice Cream" is present. "Frozen" describes the state. "Vanilla" describes the flavor. Looks good.\n    *   Final: `"Frozen Vanilla Ice Cream"`\n\n3.  **"Athena - Fire Roasted Grilled Eggplant Pulp - 2 kg"**\n    *   Remove metadata: `2 kg`\n    *   Remove brand: `Athena`\n    *   Result: `Fire Roasted Grilled Eggplant Pulp`\n    *   More than 3 words. Core product type "Eggplant Pulp" is present. Descriptors "Fire Roasted", "Grilled" are good.\n    *   Final: `"Fire Roasted Grilled Eggplant Pulp"`\n\n4.  **"Chef\'s Quality - 12\\" Whole Wheat Soft Tortillas or Wraps - 12ct Pack"**\n    *   Remove metadata: `12\\"`, `12ct Pack`\n    *   Remove brand: `Chef\'s Quality`\n    *   Result: `Whole Wheat Soft Tortillas or Wraps`\n    *   More than 3 words. Core product type "Tortillas or Wraps" is present. Descriptors "Whole Wheat", "Soft" are good.\n    *   Final: `"Whole Wheat Soft Tortillas Wraps"` (making \'or\' implicit)\n\n5.  **"Sunset - TF-81-RD - 8\\" Hinged TFPP Container - 150 ct"**\n    *   Remove metadata: `TF-81-RD`, `8\\"`, `150 ct`\n    *   Remove brand: `Sunset`\n    *   Result: `Hinged TFPP Container`\n    *   "TFPP" is likely an abbreviation for Thermoformed Polypropylene. Should I expand it? The instructions mention expanding abbreviations, but maybe not technical material codes unless common. Let\'s keep it for now.\n    *   Result: `Hinged TFPP Container`\n    *   3 words. "Container" is the core product. "Hinged" describes a feature. "TFPP" describes the material. Looks reasonable. Is it a food product though? The prompt asks for food products, but this seems like packaging. I\'ll process it as requested. Perhaps "Hinged Plastic Food Container"? "TFPP" implies plastic. Let\'s make it more descriptive for a general audience.\n    *   Final: `"Hinged Plastic Food Container"`\n\nConsolidate the results into a JSON array.',
            },
            {
              text: '[\n  "Wooden Bamboo Skewers",\n  "Frozen Vanilla Ice Cream",\n  "Fire Roasted Grilled Eggplant Pulp",\n  "Whole Wheat Soft Tortillas Wraps",\n  "Hinged Plastic Food Container"\n]',
            },
          ],
        },
        {
          role: "user",
          parts: [{ text: '["MIX SPRINTS HAZELNUT 10/2.4#"]' }],
        },
        {
          role: "model",
          parts: [
            {
              text: 'The user wants me to process a list containing a single food product name: "MIX SPRINTS HAZELNUT 10/2.4#".\nMy goal is to enhance this name according to the specified criteria:\n1.  **Remove metadata:** "10/2.4#" seems like metadata (count/weight).\n2.  **Replace abbreviations:** "MIX" could be "Mixed". "SPRINTS" is likely a typo or part of a brand/product name, but doesn\'t look like a standard abbreviation. I need to make a judgment call. "SPRINTS" could refer to "SPRINKLES" maybe? Or maybe it\'s a brand name like "Sprint Mix". Given "HAZELNUT", "SPRINKLES" makes sense. Let\'s assume it\'s "Mixed Hazelnut Sprinkles".\n3.  **Add missing core product type:** "Sprinkles" is the core product type. "Mixed Hazelnut Sprinkles" already includes it.\n4.  **Remove brand names (optional):** "SPRINTS" could be a brand, but I\'m interpreting it as part of the description (sprinkles). If it were clearly a brand like "Nestle", I would remove it. I\'ll keep it for now, perhaps rephrasing.\n5.  **Ensure minimum 3 words:** "Mixed Hazelnut Sprinkles" has 3 words.\n6.  **Add descriptive words:** "Mixed" and "Hazelnut" are descriptive. "Sprinkles" describes the presentation. Is there anything else? Maybe "Edible Mixed Hazelnut Sprinkles" or "Mixed Hazelnut Baking Sprinkles"? "Baking" seems plausible for sprinkles.\n\nLet\'s refine:\n-   Original: "MIX SPRINTS HAZELNUT 10/2.4#"\n-   Remove metadata: "MIX SPRINTS HAZELNUT"\n-   Expand abbreviation: "Mixed SPRINTS HAZELNUT"\n-   Interpret "SPRINTS": Could be "SPRINKLES". Let\'s try that: "Mixed Hazelnut Sprinkles". This has 3 words and seems descriptive.\n-   Alternative interpretation: "SPRINTS" might be a specific product type or brand. If I can\'t be sure, I should try to keep the essence. "Mixed Hazelnut Sprint Mix"? "Mixed Hazelnut Snack Mix"? The presence of "HAZELNUT" suggests a nut mix or a topping. "Sprinkles" seems most likely in a food context.\n-   Let\'s go with "Mixed Hazelnut Sprinkles". It meets all criteria.\n\nDouble-checking criteria:\n-   Metadata removed: Yes (10/2.4#).\n-   Abbreviations expanded: Yes (MIX -> Mixed).\n-   Core product type present: Yes (Sprinkles).\n-   Brand name removed: N/A (assuming SPRINTS is descriptive).\n-   Minimum 3 words: Yes (Mixed Hazelnut Sprinkles).\n-   Descriptive words added: Yes (Mixed, Hazelnut, Sprinkles).\n\nThe output should be a JSON array containing the processed name.',
            },
            { text: '[\n  "Mixed Hazelnut Sprinkles"\n]' },
          ],
        },
        {
          role: "user",
          parts: [{ text: '["MIX SPRINTS HAZELNUT 10/2.4#"]\n\n' }],
        },
        {
          role: "model",
          parts: [{ text: '[\n  "Mixed Hazelnut Sprinkles"\n]' }],
        },
        {
          role: "user",
          parts: [
            {
              text: "do not replace sprints with sprinkles they are different\ndo not remove important words that describe the state like frozen , whole, fresh, sliced, diced, sprints\n",
            },
          ],
        },
        {
          role: "model",
          parts: [{ text: '[\n  "Mixed Sprints Hazelnut"\n]' }],
        },
        {
          role: "user",
          parts: [{ text: "[MIX SPRINTS POPCORN 1/2#]" }],
        },
        {
          role: "model",
          parts: [{ text: '[\n  "Mixed Sprints Popcorn"\n]' }],
        },
      ],
    });

    // Send the current batch of names as a JSON string
    const result = await chatSession.sendMessage(
      JSON.stringify(productNamesBatch)
    );
    const responseText = result.response.text();

    // --- Parse and Validate the Response ---
    let enhancedNames;
    try {
      enhancedNames = JSON.parse(responseText);
    } catch (parseError) {
      console.error("  Error: Failed to parse Gemini response as JSON.");
      console.error("  Raw Response:", responseText); // Log raw response for debugging
      throw new Error("Invalid JSON response from AI");
    }

    // Validation remains the same as user's code
    if (
      !Array.isArray(enhancedNames) ||
      enhancedNames.length !== productNamesBatch.length
    ) {
      console.error(
        `  Error: Gemini response is not an array or length mismatch. Expected ${productNamesBatch.length}, Got ${enhancedNames?.length}`
      );
      console.error("  Parsed Response:", enhancedNames);
      throw new Error("AI response length mismatch or invalid format");
    }

    console.log(
      `  Received ${enhancedNames.length} enhanced names from Gemini.`
    );
    return enhancedNames; // Should be an array of strings
  } catch (error) {
    console.error("  Error during Gemini API call:", error);
    // Propagate the error to stop processing if needed, or implement retry logic
    throw error;
  }
}

// --- Main Processing Function ---
async function processProducts() {
  console.log(`Starting product enhancement process...`);

  // ***** START: Added logic to read progress file *****
  let startIndex = 0; // Default starting index
  try {
    const progressData = await fs.readFile(PROGRESS_FILE, "utf8");
    const progress = JSON.parse(progressData);
    if (progress && typeof progress.nextIndex === "number") {
      startIndex = progress.nextIndex;
      console.log(`Resuming from product index: ${startIndex}`);
    }
  } catch (error) {
    if (error.code === "ENOENT") {
      console.log(
        `No progress file found (${PROGRESS_FILE}), starting from the beginning.`
      );
    } else {
      console.warn(
        `Could not read progress file (${PROGRESS_FILE}), starting from beginning. Error: ${error.message}`
      );
    }
    startIndex = 0; // Ensure start from 0 if error or file not found
  }
  // ***** END: Added logic to read progress file *****

  // ============================================================
  // === START: MODIFIED DATA LOADING LOGIC (THE ONLY CHANGE) ===
  // ============================================================
  let allProducts;
  try {
    console.log(
      `Attempting to load existing enhanced data from: ${OUTPUT_JSON_FILE}`
    );
    const existingOutputData = await fs.readFile(OUTPUT_JSON_FILE, "utf8");
    allProducts = JSON.parse(existingOutputData);
    console.log(
      `Successfully loaded ${allProducts.length} products from existing output file (${OUTPUT_JSON_FILE}).`
    );
    // Basic validation: check if it's an array
    if (!Array.isArray(allProducts)) {
      console.error(
        `Error: Loaded data from ${OUTPUT_JSON_FILE} is not an array. Check the file.`
      );
      process.exit(1);
    }
  } catch (outputError) {
    if (outputError.code === "ENOENT") {
      // If output file doesn't exist
      console.log(
        `No existing output file found (${OUTPUT_JSON_FILE}). Loading original data from: ${INPUT_JSON_FILE}`
      );
      try {
        const rawData = await fs.readFile(INPUT_JSON_FILE, "utf8");
        allProducts = JSON.parse(rawData);
        console.log(
          `Successfully loaded ${allProducts.length} products from input file (${INPUT_JSON_FILE}).`
        );
        // Basic validation
        if (!Array.isArray(allProducts)) {
          console.error(
            `Error: Loaded data from ${INPUT_JSON_FILE} is not an array.`
          );
          process.exit(1);
        }
      } catch (inputError) {
        console.error(
          `FATAL: Error reading input file ${INPUT_JSON_FILE} after failing to find output file:`,
          inputError
        );
        process.exit(1);
      }
    } else {
      // If output file exists but there was another error reading it
      console.error(
        `FATAL: Error reading existing output file ${OUTPUT_JSON_FILE}. Check file integrity or permissions.`,
        outputError
      );
      process.exit(1); // Exit if the output file exists but is corrupt/unreadable
    }
  }
  // ============================================================
  // === END: MODIFIED DATA LOADING LOGIC ===
  // ============================================================

  // --- Check if already completed ---
  if (allProducts.length === 0) {
    console.log("Loaded data is empty. Nothing to process.");
    return;
  }
  if (startIndex >= allProducts.length) {
    console.log("Progress file indicates processing is already complete.");
    return; // Exit if processing was already finished
  }
  // --- End Check ---

  console.log(`Loaded ${allProducts.length} products to process.`); // Adjusted log msg
  // Calculate remaining batches based on startIndex
  const remainingProducts = allProducts.length - startIndex;
  const totalBatches = Math.ceil(remainingProducts / BATCH_SIZE);
  console.log(
    `Processing in batches of ${BATCH_SIZE}. Starting from index ${startIndex}. Remaining batches: ${totalBatches}`
  );

  // ***** START: Modified loop to start from startIndex *****
  // Process products in batches starting from startIndex
  for (let i = startIndex; i < allProducts.length; i += BATCH_SIZE) {
    // Calculate batch index relative to the resumed start
    const currentBatchNumber = Math.floor((i - startIndex) / BATCH_SIZE) + 1;
    // ***** END: Modified loop to start from startIndex *****

    const batch = allProducts.slice(
      i,
      Math.min(i + BATCH_SIZE, allProducts.length)
    ); // Use Math.min for safety
    const productNamesBatch = batch.map((product) => product.name); // Keep original logic of sending names only

    if (productNamesBatch.length === 0) continue;

    // Use currentBatchNumber for logging
    console.log(
      `\nProcessing Batch ${currentBatchNumber} of ${totalBatches} (Products ${
        i + 1
      } to ${i + batch.length})...`
    );
    // Log the first product name of the current batch for reference
    if (batch.length > 0) {
      console.log(`  First product name in batch: "${batch[0].name}"`);
    }

    try {
      const enhancedNames = await generateEnhancedNames(productNamesBatch); // Keep calling with names only

      if (enhancedNames.length === batch.length) {
        // Keep checking against batch length
        for (let j = 0; j < batch.length; j++) {
          allProducts[i + j].Nname = enhancedNames[j]; // Update Nname in the main array
        }
        console.log(
          `  Successfully added enhanced names for Batch ${currentBatchNumber}.` // Use currentBatchNumber
        );

        // --- SAVE AFTER EACH BATCH ---
        console.log(
          `  Saving progress to ${OUTPUT_JSON_FILE} after Batch ${currentBatchNumber}...` // Use currentBatchNumber
        );
        try {
          // Stringify the *current state* of the entire allProducts array
          const currentOutputData = JSON.stringify(allProducts, null, 2);
          // Overwrite the output file with the current state
          await fs.writeFile(OUTPUT_JSON_FILE, currentOutputData, "utf8");
          console.log(
            `  Successfully saved progress data to ${OUTPUT_JSON_FILE}.` // Adjusted log
          );

          // ***** START: Added logic to save progress file *****
          const nextIndexToProcess = i + batch.length; // Calculate the starting index for the *next* batch
          const progressState = JSON.stringify({
            nextIndex: nextIndexToProcess,
          });
          await fs.writeFile(PROGRESS_FILE, progressState, "utf8");
          console.log(
            `  Successfully saved progress marker (${PROGRESS_FILE}) (next index: ${nextIndexToProcess}).`
          );
          // ***** END: Added logic to save progress file *****
        } catch (writeError) {
          console.error(
            `  FATAL ERROR writing output file (${OUTPUT_JSON_FILE}) OR progress file (${PROGRESS_FILE}) after Batch ${currentBatchNumber}:`, // Use currentBatchNumber
            writeError
          );
          console.error(
            `  >>> Progress not updated. Batch ${currentBatchNumber} (starting index ${i}) will need to be retried on next run. <<<`
          );
          process.exit(1); // Exit on write error
        }
        // --- END SAVE AFTER EACH BATCH ---
      } else {
        console.error(
          `  Error: Mismatch in returned names count for Batch ${currentBatchNumber}. Skipping update for this batch.` // Use currentBatchNumber
        );
        process.exit(1); // Exit on mismatch for safety
      }
    } catch (error) {
      // Keep original error handling, just update log message
      console.error(
        `\n  -----------------------------------------------------`
      );
      console.error(
        `  Failed to process Batch ${currentBatchNumber} (starting index ${i}). Error: ${error.message}` // Use currentBatchNumber
      );
      console.error(`  Progress file (${PROGRESS_FILE}) has NOT been updated.`);
      console.error(
        `  The script will attempt to resume from index ${i} on the next run.`
      );
      console.error(
        `  -----------------------------------------------------\n`
      );
      process.exit(1);
    }

    // Keep original delay logic
    if (i + BATCH_SIZE < allProducts.length && DELAY_BETWEEN_BATCHES_MS > 0) {
      console.log(
        `  Waiting ${
          DELAY_BETWEEN_BATCHES_MS / 1000
        } seconds before next batch...` // Adjusted log slightly
      );
      await delay(DELAY_BETWEEN_BATCHES_MS);
    }
  }

  // Keep original final completion message
  console.log(
    "\nProcessing complete. Data saved incrementally after each batch."
  );
}

// --- Run the main function ---
processProducts();
