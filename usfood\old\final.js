const puppeteer = require("puppeteer");
const fs = require("fs");
const path = require("path");

(async () => {
  // Load the JSON file containing categories
  const catJsonPath = path.resolve(__dirname, "emptyrevise.json");
  const categories = JSON.parse(fs.readFileSync(catJsonPath, "utf8"));

  // Load progress (if file exists)
  const progressFilePath = path.resolve(__dirname, "progress.json");
  let progress = {
    scrapedSubcategories: [], // Array of subcategory file names
    scrapedProducts: {}, // Track product numbers per subcategory
  };
  if (fs.existsSync(progressFilePath)) {
    const existingData = JSON.parse(fs.readFileSync(progressFilePath));
    progress.scrapedSubcategories = existingData.scrapedSubcategories || [];
    progress.scrapedProducts = existingData.scrapedProducts || {};
    console.log(
      `Loaded ${
        progress.scrapedSubcategories.length
      } scraped subcategories and ${
        Object.keys(progress.scrapedProducts).length
      } scraped products from ${progressFilePath}`
    );
  }

  // Launch browser
  const browser = await puppeteer.launch({
    headless: false,
    userDataDir: "./user_data",
  });

  const page = await browser.newPage();
  page.setDefaultNavigationTimeout(50000); // Set timeout to 20 seconds

  // Function to scrape product data
  const scrapeProductData = async (productElement) => {
    const productNumber = await productElement.$eval(
      '[data-cy^="product-number"]',
      (el) => el.innerText.trim()
    );

    // Skip if product already scraped
    if (progress.scrapedProducts[productNumber]) return null;

    const productData = {
      productNumber,
      productDescription: await productElement.$eval(
        '[data-cy="product-description-text"]',
        (el) => el.innerText.trim()
      ),
      brand: await productElement
        .$eval(".brand-row", (el) => el.innerText.trim())
        .catch(() => null),
      image: await productElement
        .$eval("app-product-card-image img[data-cy$='image']", (el) => el.src)
        .catch(() => null),
      packSize: await productElement
        .$eval('[data-cy^="product-packsize-"]', (el) => el.innerText.trim())
        .catch(() => null),
      portionPrice: await productElement
        .$eval(".number-salespack-row > p:nth-child(4)", (el) =>
          el.innerText.trim()
        )
        .catch(() => null),
      price: await productElement
        .$eval(
          ".price-text-margin, [data-cy^='product-special-order-message-']",
          (el) => el.innerText.trim()
        )
        .catch(() => null),
    };

    // Add product to scraped products set
    progress.scrapedProducts[productNumber] = true;
    return productData;
  };

  // Function to scrape products from a category URL
  const scrapeCategory = async (url, maincat, subcat) => {
    console.log(`Navigating to ${subcat} (${url})...`);
    await page.goto(url, { waitUntil: "networkidle2" });

    // Wait for the product wrapper to load
    console.log("Waiting for products to load...");
    try {
      await page.waitForSelector(".cdk-virtual-scroll-content-wrapper", {
        timeout: 60000,
      });
    } catch (error) {
      console.log(`No products found in ${subcat}. Skipping...`);
      return null; // Return null to indicate no products were found
    }

    let allProducts = [];
    let batchCount = 0;

    // Function to scrape products in the current viewport
    const scrapeProductsInViewport = async () => {
      return await page.$$eval(
        ".cdk-virtual-scroll-content-wrapper > app-selectable-search-result",
        (products) => {
          return products.map((product) => {
            const productWrapper = product.querySelector(".product-wrapper");
            if (!productWrapper) return null;

            const originalProduct = productWrapper.querySelector(
              ".original-product-card"
            );
            const alternativeProduct = productWrapper.querySelector(
              ".alternative-product-card"
            );

            return {
              originalProduct: originalProduct
                ? originalProduct.outerHTML
                : null,
              alternativeProduct: alternativeProduct
                ? alternativeProduct.outerHTML
                : null,
            };
          });
        }
      );
    };

    // Function to scroll the container
    const scrollByPixels = async (pixels) => {
      await page.evaluate((pixels) => {
        const container = document.querySelector(".cdk-virtual-scrollable");
        if (!container) throw new Error("Scroll container not found");
        container.scrollBy({ top: pixels, behavior: "smooth" });
      }, pixels);
    };

    // Start scraping
    let unchangedCount = 0;
    const maxUnchangedAttempts = 15;

    while (unchangedCount < maxUnchangedAttempts) {
      // Scrape products in the current viewport
      const products = await scrapeProductsInViewport();
      const initialCount = allProducts.length;

      for (const product of products) {
        if (product.originalProduct) {
          const originalProductElement = await page.evaluateHandle((html) => {
            const div = document.createElement("div");
            div.innerHTML = html;
            return div.firstChild;
          }, product.originalProduct);
          const originalProductData = await scrapeProductData(
            originalProductElement
          );
          if (originalProductData) {
            allProducts.push({ ...originalProductData, maincat, subcat });
            batchCount++;
          }
        }

        if (product.alternativeProduct) {
          const alternativeProductElement = await page.evaluateHandle(
            (html) => {
              const div = document.createElement("div");
              div.innerHTML = html;
              return div.firstChild;
            },
            product.alternativeProduct
          );
          const alternativeProductData = await scrapeProductData(
            alternativeProductElement
          );
          if (alternativeProductData) {
            allProducts.push({ ...alternativeProductData, maincat, subcat });
            batchCount++;
          }
        }

        // Save progress every 25 products
        if (batchCount >= 25) {
          const subcatFileName = subcat.replace(/[^a-zA-Z0-9]/g, "") + ".json";
          const subcatFilePath = path.resolve(__dirname, subcatFileName);
          fs.writeFileSync(
            subcatFilePath,
            JSON.stringify(allProducts, null, 2)
          );
          console.log(
            `Saved progress for ${batchCount} products in ${subcatFileName}.`
          );
          batchCount = 0;
        }
      }

      // Check if new products were found
      if (allProducts.length === initialCount) {
        unchangedCount++;
        console.log(
          `No new products found. Retry ${unchangedCount}/${maxUnchangedAttempts}`
        );
      } else {
        unchangedCount = 0; // Reset unchanged count if new products are found
      }

      // Scroll to load more products
      await scrollByPixels(225);
      console.log("Scrolled to load more products...");

      // Wait for new products to load
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    return allProducts;
  };

  // Loop through categories and scrape products
  for (const category of categories) {
    const { url, maincat, subcat } = category;
    const subcatFileName = subcat.replace(/[^a-zA-Z0-9]/g, "") + ".json";
    const subcatFilePath = path.resolve(__dirname, subcatFileName);

    // Skip if subcategory already scraped and not the last one
    if (
      progress.scrapedSubcategories.includes(subcatFileName) &&
      progress.scrapedSubcategories[
        progress.scrapedSubcategories.length - 1
      ] !== subcatFileName
    ) {
      console.log(`Skipping ${subcat}: Already processed.`);
      continue;
    }

    // Add subcategory to progress immediately
    progress.scrapedSubcategories.push(subcatFileName);
    fs.writeFileSync(progressFilePath, JSON.stringify(progress, null, 2));
    console.log(`Added ${subcatFileName} to progress.`);

    // Scrape the subcategory
    const products = await scrapeCategory(url, maincat, subcat);

    // Handle cases where no products were found
    if (products === null) {
      fs.writeFileSync(subcatFilePath, JSON.stringify([], null, 2));
      console.log(`Created empty file for ${subcatFileName}.`);
      continue;
    }

    // Save the subcategory products to its JSON file
    fs.writeFileSync(subcatFilePath, JSON.stringify(products, null, 2));
    console.log(`Saved ${products.length} products to ${subcatFileName}.`);
  }

  await browser.close();
})();
