const puppeteer = require('puppeteer');
const fs = require('fs');

// List of seller files to process
const sellerFiles = ['depot.json', 'chef.json', 'greco.json', 'perf.json','sham.json'];

// Load or initialize progress
let progress;
if (fs.existsSync('progress.json')) {
  progress = JSON.parse(fs.readFileSync('progress.json', 'utf8'));
} else {
  progress = { currentSellerIndex: 0, currentProductIndex: 0 };
}

// Function to ensure list view is active
async function ensureListView(page, Nname) {
  let productWrapper = null;
  const cardLayout = await page.$('.catalog-cards-wrapper');
  if (cardLayout) {
    console.log(`Card layout detected. Toggling to list view for search: ${Nname}`);
    try {
      await page.waitForSelector('[data-id="catalog_list_view_button"]', { visible: true, timeout: 20000 });
      const listViewButton = await page.$('[data-id="catalog_list_view_button"]');
      if (listViewButton) {
        const boundingBox = await listViewButton.boundingBox();
        if (boundingBox) {
          try {
            await page.click('[data-id="catalog_list_view_button"]');
          } catch (err) {
            console.log('Direct click failed, attempting evaluate click...');
            await page.evaluate(el => el.click(), listViewButton);
          }
          console.log('Clicked list view button. Waiting for list view to load...');
          await new Promise(resolve => setTimeout(resolve, 1000)); // Brief delay for transition
          try {
            productWrapper = await page.waitForSelector('div.data-grid-body', { timeout: 50000 });
          } catch (err) {
            console.error(`Error waiting for list view to load: ${err.message}`);
            productWrapper = null;
          }
          if (!productWrapper) {
            console.error('List view still not active after click attempt.');
          }
        } else {
          console.error('List view button is not interactable (no bounding box).');
        }
      } else {
        console.error('List view button not found.');
      }
    } catch (err) {
      console.error(`Error finding list view button: ${err.message}`);
    }
  } else {
    console.log(`No card layout detected. Assuming list view for search: ${Nname}`);
    productWrapper = await page.$('div.data-grid-body');
    if (!productWrapper) {
      console.error('List view not found despite no card layout.');
    }
  }
  return !!productWrapper;
}

// Function to scrape Sysco search results for a given Nname
async function scrapeSyscoMatches(page, Nname) {
    const searchUrl = `https://shop.sysco.com/app/catalog?q=${encodeURIComponent(Nname)}`;
    await page.goto(searchUrl, { waitUntil: 'networkidle2' });
    // Wait for search results or no results message


    // Ensure list view is active only if not already in list view
    await new Promise(resolve => setTimeout(resolve, 2000)); // Brief delay for transition
    const isListViewActive = await ensureListView(page, Nname);
    if (!isListViewActive) {
        console.log(`Error: Could not switch to list view or find product wrapper for search: ${Nname}`);
        return {};
    }
    await page.waitForSelector('div.data-grid-body, [data-id="no_results_message_header"]', { timeout: 60000 });
    // Check for no results
    const noResults = await page.$('[data-id="no_results_message_header"]');
    if (noResults) {
        return {};
    }
    let syscoMatches = {};
    let hasMorePages = true;
    let paginated = false;

    while (hasMorePages) {
        // Scrape products on the current page from list view
        const products = await page.$$eval('div.data-grid-body div.row.data-grid-row.v2-product-list-item', (rows) => {
            return rows.map(row => {
                const productNumber = row.querySelector('label[data-id="lbl_product_id"]')?.innerText;
                const name = row.querySelector('div.productName-wrapper')?.innerText;
                return productNumber && name ? { productNumber, name } : null;
            }).filter(p => p);
        });

        // Add products to syscoMatches
        products.forEach(p => {
            syscoMatches[p.productNumber] = p.name;
        });

        // Check pagination
        const showingText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsPageText"]', el => el.innerText).catch(() => null);
        const totalText = await page.$eval('[data-id="ss-searchPage-header-label-searchResultsTotalText"]', el => el.innerText).catch(() => null);
        
        if (showingText && totalText) {
            const Y = parseInt(showingText.split('-')[1]?.trim());
            const Z = parseInt(totalText.split(' ')[1]?.trim());
            if (Y >= Z || Y >= 24 ) {
                hasMorePages = false;
            } else {
                paginated = true;
                // Navigate to the next page
                const currentUrl = page.url();
                const url = new URL(currentUrl);
                const pageNum = parseInt(url.searchParams.get('page') || '1') + 1;
                url.searchParams.set('page', pageNum);
                await page.goto(url.toString(), { waitUntil: 'networkidle2' });
                await ensureListView(page, Nname);
                await page.waitForSelector('div.data-grid-body, [data-id="no_results_message_header"]', { timeout: 60000 });
                const productWrapper = await page.$('div.data-grid-body');
                if (!productWrapper) {
                    console.log(`List view not found on next page for search: ${Nname}`);
                    hasMorePages = false;
                }
            }
        } else {
            hasMorePages = false; // Assume no more pages if pagination info is missing
        }
    }

    return syscoMatches;
}

(async () => {
  // Launch browser
  const browser = await puppeteer.launch({
    headless: true, // Set to true for production if no manual login needed
    userDataDir: './user_data', // Persists login session
    defaultViewport: null,
  });
  const page = await browser.newPage();


  // Process each seller file
  for (let sellerIndex = progress.currentSellerIndex; sellerIndex < sellerFiles.length; sellerIndex++) {
    const sellerFile = sellerFiles[sellerIndex];
    const sellerName = sellerFile.replace('.json', '');
    const outputFile = `${sellerName}_matched.json`;

    // Load existing output or initialize
    let outputArray = [];
    if (fs.existsSync(outputFile)) {
      outputArray = JSON.parse(fs.readFileSync(outputFile, 'utf8'));
    }

    // Load seller products
    const sellerProducts = JSON.parse(fs.readFileSync(sellerFile, 'utf8'));
    let currentProductIndex = outputArray.length;

    // Process each product
    for (let i = currentProductIndex; i < sellerProducts.length; i++) {
      const product = sellerProducts[i];
      const Nname = product.Nname;

      console.log(`Processing ${sellerName} product ${i + 1}/${sellerProducts.length}: ${Nname}`);
      
      // Scrape Sysco matches
      const syscoMatches = await scrapeSyscoMatches(page, Nname);

      // Create output entry
      const entry = {
        [`${sellerName}Product`]: {
          [product.productnumber]: product.name
        },
        syscoMatches
      };

      // Add to output array and save
      outputArray.push(entry);
      fs.writeFileSync(outputFile, JSON.stringify(outputArray, null, 2));

      // Update progress
      progress.currentSellerIndex = sellerIndex;
      progress.currentProductIndex = i + 1;
      fs.writeFileSync('progress.json', JSON.stringify(progress, null, 2));
    }

    // Reset product index for the next seller
    progress.currentProductIndex = 0;
    fs.writeFileSync('progress.json', JSON.stringify(progress, null, 2));
  }

  await browser.close();
  console.log('Scraping completed successfully.');
})();